using MongoDB.Bson;
using MongoDB.Driver;
using TodoAPI.Models;

namespace TodoAPI.Services
{
    public class TodoService(IMongoDatabase database)
    {
        private readonly IMongoCollection<Todo> _todos = database.GetCollection<Todo>("Todos");

        public virtual async Task<List<Todo>> GetTodosAsync()
        {
            return await _todos.Find(todo => true).ToListAsync();
        }

        public virtual async Task<Todo?> GetTodoAsync(string id)
        {
            return await _todos.Find(todo => todo.Id == id).FirstOrDefaultAsync();
        }

        public virtual async Task<Todo> CreateTodoAsync(Todo todo)
        {
            await _todos.InsertOneAsync(todo);
            return todo;
        }

        public virtual async Task UpdateTodoAsync(string id, Todo newTodo)
        {
            await _todos.ReplaceOneAsync(todo => todo.Id == id, newTodo);
        }

        public virtual async Task DeleteTodoAsync(string id)
        {
            await _todos.DeleteOneAsync(todo => todo.Id == id);
        }

        public virtual async Task<List<Todo>> SearchTodosByNameAsync(string name)
        {
            var filter = Builders<Todo>.Filter.Regex("Name", new BsonRegularExpression(name, "i"));
            return await _todos.Find(filter).ToListAsync();
        }
    }
}