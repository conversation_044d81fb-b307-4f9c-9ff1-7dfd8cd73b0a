using Microsoft.AspNetCore.Mvc;
using TodoAPI.Models;
using TodoAPI.Services;

namespace TodoAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController(UserService userService) : ControllerBase
    {
        private readonly UserService _userService = userService;

        public class RegisterModel
        {
            public string Username { get; set; } = null!;
            public string Password { get; set; } = null!;
            public string? Email { get; set; }
        }

        public class LoginModel
        {
            public string Username { get; set; } = null!;
            public string Password { get; set; } = null!;
        }

        public class AuthResponse
        {
            public string Token { get; set; } = null!;
            public string Username { get; set; } = null!;
        }

        [HttpPost("register")]
        public async Task<ActionResult<AuthResponse>> Register([FromBody] RegisterModel model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var user = await _userService.RegisterUserAsync(model.Username, model.Password, model.Email);
                var token = _userService.GenerateJwtToken(user.Username);

                return Ok(new AuthResponse
                {
                    Token = token,
                    Username = user.Username
                });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        [HttpPost("login")]
        public async Task<ActionResult<AuthResponse>> Login([FromBody] LoginModel model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var isValid = await _userService.ValidateUserAsync(model.Username, model.Password);
            if (!isValid)
            {
                return Unauthorized(new { message = "Invalid username or password" });
            }

            var token = _userService.GenerateJwtToken(model.Username);
            return Ok(new AuthResponse
            {
                Token = token,
                Username = model.Username
            });
        }
    }
}
