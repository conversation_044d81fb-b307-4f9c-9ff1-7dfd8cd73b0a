using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;

namespace TodoAPI.Models
{
    public class User
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string? Id { get; set; }

        [BsonElement("Username")]
        [Required]
        public string Username { get; set; } = null!;

        [BsonElement("PasswordHash")]
        [Required]
        public string PasswordHash { get; set; } = null!;

        [BsonElement("Email")]
        [EmailAddress]
        public string? Email { get; set; }

        [BsonElement("CreatedAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
}
