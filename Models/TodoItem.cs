using System.Text.Json.Serialization;
using System.ComponentModel.DataAnnotations;

namespace TodoAPI.Models;

public class TodoItem
{
    [JsonPropertyName("id")]
    public string? Id { get; set; }

    [JsonPropertyName("name")]
    [Required(ErrorMessage = "Name is required.")]
    [MinLength(1, ErrorMessage = "Name cannot be empty.")]
    public string? Name { get; set; }

    [JsonPropertyName("completed")]
    public bool Completed { get; set; }
}
