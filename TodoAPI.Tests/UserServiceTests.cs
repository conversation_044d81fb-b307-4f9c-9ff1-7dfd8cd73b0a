using Xunit;
using Moq;
using MongoDB.Driver;
using Microsoft.Extensions.Configuration;
using TodoAPI.Models;
using TodoAPI.Services;
using System.IdentityModel.Tokens.Jwt;

namespace TodoAPI.TodoAPI.Tests
{
    public class UserServiceTests
    {
        private readonly Mock<IMongoCollection<User>> _mockCollection;
        private readonly Mock<IMongoDatabase> _mockDatabase;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly UserService _service;

        public UserServiceTests()
        {
            _mockCollection = new Mock<IMongoCollection<User>>();
            _mockDatabase = new Mock<IMongoDatabase>();
            _mockConfiguration = new Mock<IConfiguration>();
            
            _mockDatabase.Setup(db => db.GetCollection<User>("Users", null))
                        .Returns(_mockCollection.Object);

            // Setup JWT configuration
            _mockConfiguration.Setup(c => c["Jwt:Key"]).Returns("ThisIsASecretKeyForJWTTokenGenerationThatIsLongEnough");
            _mockConfiguration.Setup(c => c["Jwt:Issuer"]).Returns("TodoAPI");
            _mockConfiguration.Setup(c => c["Jwt:Audience"]).Returns("TodoAPI");
            
            _service = new UserService(_mockDatabase.Object, _mockConfiguration.Object);
        }

        [Fact]
        public async Task GetUserByUsernameAsync_WithExistingUser_ReturnsUser()
        {
            // Arrange
            var username = "testuser";
            var user = new User 
            { 
                Id = "507f1f77bcf86cd799439011", 
                Username = username, 
                PasswordHash = "hashedpassword",
                Email = "<EMAIL>",
                CreatedAt = DateTime.UtcNow
            };

            var mockCursor = new Mock<IAsyncCursor<User>>();
            mockCursor.Setup(_ => _.Current).Returns(new List<User> { user });
            mockCursor.SetupSequence(_ => _.MoveNext(It.IsAny<CancellationToken>()))
                     .Returns(true)
                     .Returns(false);
            mockCursor.SetupSequence(_ => _.MoveNextAsync(It.IsAny<CancellationToken>()))
                     .ReturnsAsync(true)
                     .ReturnsAsync(false);

            _mockCollection.Setup(c => c.FindAsync(
                It.IsAny<FilterDefinition<User>>(),
                It.IsAny<FindOptions<User, User>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockCursor.Object);

            // Act
            var result = await _service.GetUserByUsernameAsync(username);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(username, result.Username);
            Assert.Equal("<EMAIL>", result.Email);
        }

        [Fact]
        public async Task GetUserByUsernameAsync_WithNonExistentUser_ReturnsNull()
        {
            // Arrange
            var username = "nonexistentuser";

            var mockCursor = new Mock<IAsyncCursor<User>>();
            mockCursor.Setup(_ => _.Current).Returns(new List<User>());
            mockCursor.SetupSequence(_ => _.MoveNext(It.IsAny<CancellationToken>()))
                     .Returns(false);
            mockCursor.SetupSequence(_ => _.MoveNextAsync(It.IsAny<CancellationToken>()))
                     .ReturnsAsync(false);

            _mockCollection.Setup(c => c.FindAsync(
                It.IsAny<FilterDefinition<User>>(),
                It.IsAny<FindOptions<User, User>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockCursor.Object);

            // Act
            var result = await _service.GetUserByUsernameAsync(username);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task RegisterUserAsync_WithNewUser_CreatesAndReturnsUser()
        {
            // Arrange
            var username = "newuser";
            var password = "password123";
            var email = "<EMAIL>";

            // Setup to return null for existing user check
            var mockCursor = new Mock<IAsyncCursor<User>>();
            mockCursor.Setup(_ => _.Current).Returns(new List<User>());
            mockCursor.SetupSequence(_ => _.MoveNext(It.IsAny<CancellationToken>()))
                     .Returns(false);
            mockCursor.SetupSequence(_ => _.MoveNextAsync(It.IsAny<CancellationToken>()))
                     .ReturnsAsync(false);

            _mockCollection.Setup(c => c.FindAsync(
                It.IsAny<FilterDefinition<User>>(),
                It.IsAny<FindOptions<User, User>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockCursor.Object);

            _mockCollection.Setup(c => c.InsertOneAsync(
                It.IsAny<User>(),
                It.IsAny<InsertOneOptions>(),
                It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _service.RegisterUserAsync(username, password, email);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(username, result.Username);
            Assert.Equal(email, result.Email);
            Assert.NotNull(result.PasswordHash);
            Assert.NotEqual(password, result.PasswordHash); // Password should be hashed
            Assert.True(BCrypt.Net.BCrypt.Verify(password, result.PasswordHash)); // Verify hash is correct
            
            _mockCollection.Verify(c => c.InsertOneAsync(
                It.IsAny<User>(),
                It.IsAny<InsertOneOptions>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task RegisterUserAsync_WithExistingUsername_ThrowsInvalidOperationException()
        {
            // Arrange
            var username = "existinguser";
            var password = "password123";
            var existingUser = new User 
            { 
                Username = username, 
                PasswordHash = "existinghash",
                CreatedAt = DateTime.UtcNow
            };

            var mockCursor = new Mock<IAsyncCursor<User>>();
            mockCursor.Setup(_ => _.Current).Returns(new List<User> { existingUser });
            mockCursor.SetupSequence(_ => _.MoveNext(It.IsAny<CancellationToken>()))
                     .Returns(true)
                     .Returns(false);
            mockCursor.SetupSequence(_ => _.MoveNextAsync(It.IsAny<CancellationToken>()))
                     .ReturnsAsync(true)
                     .ReturnsAsync(false);

            _mockCollection.Setup(c => c.FindAsync(
                It.IsAny<FilterDefinition<User>>(),
                It.IsAny<FindOptions<User, User>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockCursor.Object);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(
                () => _service.RegisterUserAsync(username, password));
            
            Assert.Equal("Username already exists", exception.Message);
        }

        [Fact]
        public async Task ValidateUserAsync_WithValidCredentials_ReturnsTrue()
        {
            // Arrange
            var username = "testuser";
            var password = "password123";
            var hashedPassword = BCrypt.Net.BCrypt.HashPassword(password);
            var user = new User 
            { 
                Username = username, 
                PasswordHash = hashedPassword,
                CreatedAt = DateTime.UtcNow
            };

            var mockCursor = new Mock<IAsyncCursor<User>>();
            mockCursor.Setup(_ => _.Current).Returns(new List<User> { user });
            mockCursor.SetupSequence(_ => _.MoveNext(It.IsAny<CancellationToken>()))
                     .Returns(true)
                     .Returns(false);
            mockCursor.SetupSequence(_ => _.MoveNextAsync(It.IsAny<CancellationToken>()))
                     .ReturnsAsync(true)
                     .ReturnsAsync(false);

            _mockCollection.Setup(c => c.FindAsync(
                It.IsAny<FilterDefinition<User>>(),
                It.IsAny<FindOptions<User, User>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockCursor.Object);

            // Act
            var result = await _service.ValidateUserAsync(username, password);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task ValidateUserAsync_WithInvalidPassword_ReturnsFalse()
        {
            // Arrange
            var username = "testuser";
            var correctPassword = "password123";
            var wrongPassword = "wrongpassword";
            var hashedPassword = BCrypt.Net.BCrypt.HashPassword(correctPassword);
            var user = new User 
            { 
                Username = username, 
                PasswordHash = hashedPassword,
                CreatedAt = DateTime.UtcNow
            };

            var mockCursor = new Mock<IAsyncCursor<User>>();
            mockCursor.Setup(_ => _.Current).Returns(new List<User> { user });
            mockCursor.SetupSequence(_ => _.MoveNext(It.IsAny<CancellationToken>()))
                     .Returns(true)
                     .Returns(false);
            mockCursor.SetupSequence(_ => _.MoveNextAsync(It.IsAny<CancellationToken>()))
                     .ReturnsAsync(true)
                     .ReturnsAsync(false);

            _mockCollection.Setup(c => c.FindAsync(
                It.IsAny<FilterDefinition<User>>(),
                It.IsAny<FindOptions<User, User>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockCursor.Object);

            // Act
            var result = await _service.ValidateUserAsync(username, wrongPassword);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task ValidateUserAsync_WithNonExistentUser_ReturnsFalse()
        {
            // Arrange
            var username = "nonexistentuser";
            var password = "password123";

            var mockCursor = new Mock<IAsyncCursor<User>>();
            mockCursor.Setup(_ => _.Current).Returns(new List<User>());
            mockCursor.SetupSequence(_ => _.MoveNext(It.IsAny<CancellationToken>()))
                     .Returns(false);
            mockCursor.SetupSequence(_ => _.MoveNextAsync(It.IsAny<CancellationToken>()))
                     .ReturnsAsync(false);

            _mockCollection.Setup(c => c.FindAsync(
                It.IsAny<FilterDefinition<User>>(),
                It.IsAny<FindOptions<User, User>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockCursor.Object);

            // Act
            var result = await _service.ValidateUserAsync(username, password);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void GenerateJwtToken_WithValidUsername_ReturnsValidToken()
        {
            // Arrange
            var username = "testuser";

            // Act
            var token = _service.GenerateJwtToken(username);

            // Assert
            Assert.NotNull(token);
            Assert.NotEmpty(token);

            // Verify token structure
            var tokenHandler = new JwtSecurityTokenHandler();
            var jsonToken = tokenHandler.ReadJwtToken(token);
            
            Assert.Equal(username, jsonToken.Subject);
            Assert.Contains(jsonToken.Claims, c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name" && c.Value == username);
            Assert.True(jsonToken.ValidTo > DateTime.UtcNow);
        }

        [Fact]
        public void GenerateJwtToken_WithMissingJwtKey_ThrowsInvalidOperationException()
        {
            // Arrange
            var mockConfigWithoutKey = new Mock<IConfiguration>();
            mockConfigWithoutKey.Setup(c => c["Jwt:Key"]).Returns((string?)null);
            mockConfigWithoutKey.Setup(c => c["Jwt:Issuer"]).Returns("TodoAPI");
            mockConfigWithoutKey.Setup(c => c["Jwt:Audience"]).Returns("TodoAPI");

            var serviceWithoutKey = new UserService(_mockDatabase.Object, mockConfigWithoutKey.Object);

            // Act & Assert
            var exception = Assert.Throws<InvalidOperationException>(
                () => serviceWithoutKey.GenerateJwtToken("testuser"));
            
            Assert.Equal("JWT Key not configured", exception.Message);
        }
    }
}