using Xunit;
using Moq;
using Microsoft.AspNetCore.Mvc;
using TodoAPI.Controllers;
using TodoAPI.Models;
using TodoAPI.Services;
using static TodoAPI.Controllers.AuthController;

namespace TodoAPI.TodoAPI.Tests
{
    public class AuthControllerTests
    {
        private readonly Mock<UserService> _mockUserService;
        private readonly AuthController _controller;

        public AuthControllerTests()
        {
            _mockUserService = new Mock<UserService>(
                Mock.Of<MongoDB.Driver.IMongoDatabase>(), 
                Mock.Of<Microsoft.Extensions.Configuration.IConfiguration>());
            _controller = new AuthController(_mockUserService.Object);
        }

        [Fact]
        public async Task Register_WithValidModel_ReturnsOkWithAuthResponse()
        {
            // Arrange
            var registerModel = new RegisterModel
            {
                Username = "testuser",
                Password = "password123",
                Email = "<EMAIL>"
            };

            var user = new User
            {
                Id = "507f1f77bcf86cd799439011",
                Username = registerModel.Username,
                Email = registerModel.Email,
                PasswordHash = "hashedpassword",
                CreatedAt = DateTime.UtcNow
            };

            var token = "jwt-token-here";

            _mockUserService.Setup(s => s.RegisterUserAsync(
                registerModel.Username, 
                registerModel.Password, 
                registerModel.Email))
                .ReturnsAsync(user);

            _mockUserService.Setup(s => s.GenerateJwtToken(registerModel.Username))
                           .Returns(token);

            // Act
            var result = await _controller.Register(registerModel);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var authResponse = Assert.IsType<AuthResponse>(okResult.Value);
            Assert.Equal(token, authResponse.Token);
            Assert.Equal(registerModel.Username, authResponse.Username);

            _mockUserService.Verify(s => s.RegisterUserAsync(
                registerModel.Username, 
                registerModel.Password, 
                registerModel.Email), Times.Once);
            _mockUserService.Verify(s => s.GenerateJwtToken(registerModel.Username), Times.Once);
        }

        [Fact]
        public async Task Register_WithExistingUsername_ReturnsBadRequest()
        {
            // Arrange
            var registerModel = new RegisterModel
            {
                Username = "existinguser",
                Password = "password123",
                Email = "<EMAIL>"
            };

            _mockUserService.Setup(s => s.RegisterUserAsync(
                registerModel.Username, 
                registerModel.Password, 
                registerModel.Email))
                .ThrowsAsync(new InvalidOperationException("Username already exists"));

            // Act
            var result = await _controller.Register(registerModel);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
            var errorResponse = badRequestResult.Value;
            Assert.NotNull(errorResponse);
            
            // Check if the error message is present
            var messageProperty = errorResponse.GetType().GetProperty("message");
            Assert.NotNull(messageProperty);
            Assert.Equal("Username already exists", messageProperty.GetValue(errorResponse));
        }

        [Fact]
        public async Task Register_WithInvalidModel_ReturnsBadRequest()
        {
            // Arrange
            var registerModel = new RegisterModel
            {
                Username = "", // Invalid: empty username
                Password = "password123",
                Email = "<EMAIL>"
            };

            _controller.ModelState.AddModelError("Username", "Username is required");

            // Act
            var result = await _controller.Register(registerModel);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result.Result);
        }

        [Fact]
        public async Task Login_WithValidCredentials_ReturnsOkWithAuthResponse()
        {
            // Arrange
            var loginModel = new LoginModel
            {
                Username = "testuser",
                Password = "password123"
            };

            var token = "jwt-token-here";

            _mockUserService.Setup(s => s.ValidateUserAsync(loginModel.Username, loginModel.Password))
                           .ReturnsAsync(true);

            _mockUserService.Setup(s => s.GenerateJwtToken(loginModel.Username))
                           .Returns(token);

            // Act
            var result = await _controller.Login(loginModel);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var authResponse = Assert.IsType<AuthResponse>(okResult.Value);
            Assert.Equal(token, authResponse.Token);
            Assert.Equal(loginModel.Username, authResponse.Username);

            _mockUserService.Verify(s => s.ValidateUserAsync(loginModel.Username, loginModel.Password), Times.Once);
            _mockUserService.Verify(s => s.GenerateJwtToken(loginModel.Username), Times.Once);
        }

        [Fact]
        public async Task Login_WithInvalidCredentials_ReturnsUnauthorized()
        {
            // Arrange
            var loginModel = new LoginModel
            {
                Username = "testuser",
                Password = "wrongpassword"
            };

            _mockUserService.Setup(s => s.ValidateUserAsync(loginModel.Username, loginModel.Password))
                           .ReturnsAsync(false);

            // Act
            var result = await _controller.Login(loginModel);

            // Assert
            var unauthorizedResult = Assert.IsType<UnauthorizedObjectResult>(result.Result);
            var errorResponse = unauthorizedResult.Value;
            Assert.NotNull(errorResponse);
            
            // Check if the error message is present
            var messageProperty = errorResponse.GetType().GetProperty("message");
            Assert.NotNull(messageProperty);
            Assert.Equal("Invalid username or password", messageProperty.GetValue(errorResponse));

            _mockUserService.Verify(s => s.ValidateUserAsync(loginModel.Username, loginModel.Password), Times.Once);
            _mockUserService.Verify(s => s.GenerateJwtToken(It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task Login_WithInvalidModel_ReturnsBadRequest()
        {
            // Arrange
            var loginModel = new LoginModel
            {
                Username = "", // Invalid: empty username
                Password = "password123"
            };

            _controller.ModelState.AddModelError("Username", "Username is required");

            // Act
            var result = await _controller.Login(loginModel);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result.Result);
        }

        [Fact]
        public async Task Register_WithoutEmail_ReturnsOkWithAuthResponse()
        {
            // Arrange
            var registerModel = new RegisterModel
            {
                Username = "testuser",
                Password = "password123"
                // Email is null/not provided
            };

            var user = new User
            {
                Id = "507f1f77bcf86cd799439011",
                Username = registerModel.Username,
                Email = null,
                PasswordHash = "hashedpassword",
                CreatedAt = DateTime.UtcNow
            };

            var token = "jwt-token-here";

            _mockUserService.Setup(s => s.RegisterUserAsync(
                registerModel.Username, 
                registerModel.Password, 
                registerModel.Email))
                .ReturnsAsync(user);

            _mockUserService.Setup(s => s.GenerateJwtToken(registerModel.Username))
                           .Returns(token);

            // Act
            var result = await _controller.Register(registerModel);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var authResponse = Assert.IsType<AuthResponse>(okResult.Value);
            Assert.Equal(token, authResponse.Token);
            Assert.Equal(registerModel.Username, authResponse.Username);

            _mockUserService.Verify(s => s.RegisterUserAsync(
                registerModel.Username, 
                registerModel.Password, 
                null), Times.Once);
        }
    }
}